/**
 * Route metadata management system
 * Handles SEO metadata distribution across modular route system
 */

import { RouteMetadata } from '../modules/types';
import { 
  coreRouteMetadata,
  patientResourcesRouteMetadata,
  conditionsRouteMetadata,
  expertiseRouteMetadata,
  locationsRouteMetadata,
  gpResourcesRouteMetadata
} from '../modules';

export interface MetadataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingMetadata: string[];
  orphanedMetadata: string[];
}

export interface SitemapEntry {
  path: string;
  title: string;
  description: string;
  keywords: string[];
  category: string;
  priority: 'high' | 'medium' | 'low';
  changeFreq: string;
  lastModified?: string;
  module: string;
}

/**
 * Metadata Manager for the modular route system
 */
export class RouteMetadataManager {
  private static instance: RouteMetadataManager;
  private metadataCache: Map<string, RouteMetadata> = new Map();
  private lastCacheUpdate: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): RouteMetadataManager {
    if (!RouteMetadataManager.instance) {
      RouteMetadataManager.instance = new RouteMetadataManager();
    }
    return RouteMetadataManager.instance;
  }

  /**
   * Get all route metadata from all modules
   */
  getAllMetadata(): Record<string, RouteMetadata> {
    if (this.shouldRefreshCache()) {
      this.refreshMetadataCache();
    }

    const allMetadata: Record<string, RouteMetadata> = {};
    this.metadataCache.forEach((metadata, path) => {
      allMetadata[path] = metadata;
    });

    return allMetadata;
  }

  /**
   * Get metadata for a specific route
   */
  getMetadata(path: string): RouteMetadata | undefined {
    if (this.shouldRefreshCache()) {
      this.refreshMetadataCache();
    }

    return this.metadataCache.get(path);
  }

  /**
   * Get metadata by category
   */
  getMetadataByCategory(category: RouteMetadata['category']): Record<string, RouteMetadata> {
    const allMetadata = this.getAllMetadata();
    const filtered: Record<string, RouteMetadata> = {};

    Object.entries(allMetadata).forEach(([path, metadata]) => {
      if (metadata.category === category) {
        filtered[path] = metadata;
      }
    });

    return filtered;
  }

  /**
   * Get metadata by module
   */
  getMetadataByModule(moduleName: string): Record<string, RouteMetadata> {
    const allMetadata = this.getAllMetadata();
    const filtered: Record<string, RouteMetadata> = {};

    Object.entries(allMetadata).forEach(([path, metadata]) => {
      if (metadata.module === moduleName) {
        filtered[path] = metadata;
      }
    });

    return filtered;
  }

  /**
   * Validate metadata consistency
   */
  validateMetadata(registeredRoutes: string[]): MetadataValidationResult {
    const allMetadata = this.getAllMetadata();
    const metadataPaths = Object.keys(allMetadata);
    
    const result: MetadataValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      missingMetadata: [],
      orphanedMetadata: []
    };

    // Check for missing metadata
    registeredRoutes.forEach(path => {
      if (!allMetadata[path]) {
        result.missingMetadata.push(path);
        result.warnings.push(`Missing metadata for route: ${path}`);
      }
    });

    // Check for orphaned metadata
    metadataPaths.forEach(path => {
      if (!registeredRoutes.includes(path)) {
        result.orphanedMetadata.push(path);
        result.warnings.push(`Orphaned metadata for route: ${path}`);
      }
    });

    // Validate metadata structure
    Object.entries(allMetadata).forEach(([path, metadata]) => {
      if (!metadata.title || metadata.title.trim() === '') {
        result.errors.push(`Missing or empty title for route: ${path}`);
      }

      if (!metadata.description || metadata.description.trim() === '') {
        result.errors.push(`Missing or empty description for route: ${path}`);
      }

      if (metadata.title && metadata.title.length > 60) {
        result.warnings.push(`Title too long for SEO (${metadata.title.length} chars) for route: ${path}`);
      }

      if (metadata.description && metadata.description.length > 160) {
        result.warnings.push(`Description too long for SEO (${metadata.description.length} chars) for route: ${path}`);
      }

      if (!metadata.keywords || metadata.keywords.length === 0) {
        result.warnings.push(`No keywords defined for route: ${path}`);
      }
    });

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Generate sitemap data
   */
  generateSitemapData(): SitemapEntry[] {
    const allMetadata = this.getAllMetadata();
    const sitemapEntries: SitemapEntry[] = [];

    Object.entries(allMetadata).forEach(([path, metadata]) => {
      sitemapEntries.push({
        path,
        title: metadata.title,
        description: metadata.description,
        keywords: metadata.keywords || [],
        category: metadata.category,
        priority: metadata.priority,
        changeFreq: metadata.changeFreq,
        module: metadata.module || 'unknown',
        lastModified: new Date().toISOString()
      });
    });

    // Sort by priority and category
    return sitemapEntries.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority];
      const bPriority = priorityOrder[b.priority];
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return a.category.localeCompare(b.category);
    });
  }

  /**
   * Generate robots.txt content
   */
  generateRobotsTxt(baseUrl: string): string {
    const sitemapEntries = this.generateSitemapData();
    const highPriorityPaths = sitemapEntries
      .filter(entry => entry.priority === 'high')
      .map(entry => entry.path);

    return [
      'User-agent: *',
      'Allow: /',
      '',
      '# High priority pages',
      ...highPriorityPaths.map(path => `Allow: ${path}`),
      '',
      '# Disallow admin and private areas',
      'Disallow: /admin/',
      'Disallow: /api/',
      'Disallow: /_next/',
      '',
      `Sitemap: ${baseUrl}/sitemap.xml`
    ].join('\n');
  }

  /**
   * Generate structured data for routes
   */
  generateStructuredData(path: string): Record<string, unknown> | null {
    const metadata = this.getMetadata(path);
    if (!metadata) return null;

    const baseStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: metadata.title,
      description: metadata.description,
      keywords: metadata.keywords?.join(', '),
      url: path
    };

    // Add category-specific structured data
    switch (metadata.category) {
      case 'core':
        return {
          ...baseStructuredData,
          '@type': 'WebSite',
          potentialAction: {
            '@type': 'SearchAction',
            target: `${path}?q={search_term_string}`,
            'query-input': 'required name=search_term_string'
          }
        };

      case 'patient-resources':
        return {
          ...baseStructuredData,
          '@type': 'MedicalWebPage',
          medicalAudience: {
            '@type': 'Patient'
          }
        };

      case 'expertise':
        return {
          ...baseStructuredData,
          '@type': 'MedicalWebPage',
          medicalAudience: {
            '@type': 'MedicalAudience'
          },
          about: {
            '@type': 'MedicalProcedure'
          }
        };

      case 'locations':
        return {
          ...baseStructuredData,
          '@type': 'MedicalBusiness',
          '@id': path
        };

      default:
        return baseStructuredData;
    }
  }

  /**
   * Get SEO recommendations for a route
   */
  getSEORecommendations(path: string): string[] {
    const metadata = this.getMetadata(path);
    if (!metadata) return ['No metadata found for this route'];

    const recommendations: string[] = [];

    if (metadata.title.length < 30) {
      recommendations.push('Consider making the title longer for better SEO (30-60 characters recommended)');
    }

    if (metadata.description.length < 120) {
      recommendations.push('Consider making the description longer for better SEO (120-160 characters recommended)');
    }

    if (!metadata.keywords || metadata.keywords.length < 3) {
      recommendations.push('Add more relevant keywords (3-5 recommended)');
    }

    if (metadata.changeFreq === 'never' && metadata.priority === 'high') {
      recommendations.push('High priority pages should have more frequent updates');
    }

    return recommendations;
  }

  /**
   * Refresh the metadata cache
   */
  private refreshMetadataCache(): void {
    this.metadataCache.clear();

    // Combine all metadata from modules
    const allMetadata = {
      ...coreRouteMetadata,
      ...patientResourcesRouteMetadata,
      ...conditionsRouteMetadata,
      ...expertiseRouteMetadata,
      ...locationsRouteMetadata,
      ...gpResourcesRouteMetadata
    };

    Object.entries(allMetadata).forEach(([path, metadata]) => {
      this.metadataCache.set(path, metadata);
    });

    this.lastCacheUpdate = Date.now();
  }

  /**
   * Check if cache should be refreshed
   */
  private shouldRefreshCache(): boolean {
    return Date.now() - this.lastCacheUpdate > this.CACHE_TTL || this.metadataCache.size === 0;
  }

  /**
   * Get metadata statistics
   */
  getStatistics() {
    const allMetadata = this.getAllMetadata();
    const stats = {
      totalRoutes: Object.keys(allMetadata).length,
      byCategory: {} as Record<string, number>,
      byPriority: {} as Record<string, number>,
      byModule: {} as Record<string, number>,
      avgTitleLength: 0,
      avgDescriptionLength: 0,
      routesWithKeywords: 0
    };

    let totalTitleLength = 0;
    let totalDescriptionLength = 0;

    Object.values(allMetadata).forEach(metadata => {
      // Count by category
      stats.byCategory[metadata.category] = (stats.byCategory[metadata.category] || 0) + 1;
      
      // Count by priority
      stats.byPriority[metadata.priority] = (stats.byPriority[metadata.priority] || 0) + 1;
      
      // Count by module
      const module = metadata.module || 'unknown';
      stats.byModule[module] = (stats.byModule[module] || 0) + 1;
      
      // Calculate averages
      totalTitleLength += metadata.title.length;
      totalDescriptionLength += metadata.description.length;
      
      // Count routes with keywords
      if (metadata.keywords && metadata.keywords.length > 0) {
        stats.routesWithKeywords++;
      }
    });

    stats.avgTitleLength = totalTitleLength / stats.totalRoutes;
    stats.avgDescriptionLength = totalDescriptionLength / stats.totalRoutes;

    return stats;
  }
}

// Export singleton instance
export const metadataManager = RouteMetadataManager.getInstance();

// Export convenience functions
export const getAllMetadata = () => metadataManager.getAllMetadata();
export const getMetadata = (path: string) => metadataManager.getMetadata(path);
export const getMetadataByCategory = (category: RouteMetadata['category']) => 
  metadataManager.getMetadataByCategory(category);
export const validateMetadata = (routes: string[]) => metadataManager.validateMetadata(routes);
export const generateSitemapData = () => metadataManager.generateSitemapData();
