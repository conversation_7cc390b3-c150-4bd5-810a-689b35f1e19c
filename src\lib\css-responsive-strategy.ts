/**
 * CSS-First Responsive Strategy
 * Utilities and guidelines for using CSS media queries instead of JavaScript device detection
 */

import { BREAKPOINTS } from './constants';

/**
 * CSS Media Query Utilities
 * Generate consistent media queries that match our breakpoint system
 */
export const mediaQueries = {
  // Mobile-first approach
  mobile: `@media (max-width: ${BREAKPOINTS.MOBILE - 1}px)`,
  tablet: `@media (min-width: ${BREAKPOINTS.MOBILE}px) and (max-width: ${BREAKPOINTS.TABLET - 1}px)`,
  desktop: `@media (min-width: ${BREAKPOINTS.TABLET}px)`,
  largeDesktop: `@media (min-width: ${BREAKPOINTS.LARGE_DESKTOP}px)`,
  
  // Specific breakpoints
  minMobile: `@media (min-width: ${BREAKPOINTS.MOBILE}px)`,
  minTablet: `@media (min-width: ${BREAKPOINTS.TABLET}px)`,
  minDesktop: `@media (min-width: ${BREAKPOINTS.DESKTOP}px)`,
  
  // Orientation queries
  portrait: '@media (orientation: portrait)',
  landscape: '@media (orientation: landscape)',
  
  // Touch and hover capabilities
  touch: '@media (pointer: coarse)',
  hover: '@media (hover: hover)',
  noHover: '@media (hover: none)',
  
  // High DPI displays
  retina: '@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',
  
  // Reduced motion preference
  reducedMotion: '@media (prefers-reduced-motion: reduce)',
  
  // Dark mode preference
  darkMode: '@media (prefers-color-scheme: dark)',
  lightMode: '@media (prefers-color-scheme: light)',
  
  // High contrast preference
  highContrast: '@media (prefers-contrast: high)',
  
  // Print styles
  print: '@media print'
} as const;

/**
 * CSS Custom Properties for Responsive Design
 * These can be used in CSS to create responsive layouts without JavaScript
 */
export const cssCustomProperties = {
  // Breakpoint values
  '--breakpoint-mobile': `${BREAKPOINTS.MOBILE}px`,
  '--breakpoint-tablet': `${BREAKPOINTS.TABLET}px`,
  '--breakpoint-desktop': `${BREAKPOINTS.DESKTOP}px`,
  '--breakpoint-large-desktop': `${BREAKPOINTS.LARGE_DESKTOP}px`,
  
  // Container sizes
  '--container-mobile': '100%',
  '--container-tablet': '90%',
  '--container-desktop': '1200px',
  '--container-large': '1400px',
  
  // Spacing scales
  '--spacing-mobile-xs': '0.25rem',
  '--spacing-mobile-sm': '0.5rem',
  '--spacing-mobile-md': '1rem',
  '--spacing-mobile-lg': '1.5rem',
  '--spacing-mobile-xl': '2rem',
  '--spacing-mobile-2xl': '3rem',
  '--spacing-mobile-3xl': '4rem',
  
  '--spacing-desktop-xs': '0.5rem',
  '--spacing-desktop-sm': '1rem',
  '--spacing-desktop-md': '1.5rem',
  '--spacing-desktop-lg': '2rem',
  '--spacing-desktop-xl': '3rem',
  '--spacing-desktop-2xl': '4rem',
  '--spacing-desktop-3xl': '6rem',
  
  // Typography scales
  '--text-mobile-xs': '0.75rem',
  '--text-mobile-sm': '0.875rem',
  '--text-mobile-base': '1rem',
  '--text-mobile-lg': '1.125rem',
  '--text-mobile-xl': '1.25rem',
  '--text-mobile-2xl': '1.5rem',
  '--text-mobile-3xl': '1.875rem',
  
  '--text-desktop-xs': '0.75rem',
  '--text-desktop-sm': '0.875rem',
  '--text-desktop-base': '1rem',
  '--text-desktop-lg': '1.125rem',
  '--text-desktop-xl': '1.25rem',
  '--text-desktop-2xl': '1.5rem',
  '--text-desktop-3xl': '1.875rem',
  '--text-desktop-4xl': '2.25rem',
  '--text-desktop-5xl': '3rem'
} as const;

/**
 * Utility function to check if JavaScript device detection is necessary
 */
export function shouldUseJavaScriptDetection(useCase: string): boolean {
  const jsRequiredUseCases = [
    'dynamic-content-loading',
    'touch-gesture-handling',
    'viewport-calculations',
    'device-specific-apis',
    'performance-optimization',
    'conditional-script-loading',
    'complex-interaction-patterns'
  ];
  
  return jsRequiredUseCases.includes(useCase);
}

/**
 * CSS-first responsive patterns
 */
export const responsivePatterns = {
  /**
   * Container pattern - use CSS instead of JavaScript
   */
  container: `
    .responsive-container {
      width: 100%;
      max-width: var(--container-mobile);
      margin: 0 auto;
      padding: 0 var(--spacing-mobile-md);
    }
    
    ${mediaQueries.minTablet} {
      .responsive-container {
        max-width: var(--container-tablet);
        padding: 0 var(--spacing-desktop-md);
      }
    }
    
    ${mediaQueries.minDesktop} {
      .responsive-container {
        max-width: var(--container-desktop);
        padding: 0 var(--spacing-desktop-lg);
      }
    }
  `,
  
  /**
   * Grid pattern - responsive without JavaScript
   */
  grid: `
    .responsive-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-mobile-md);
    }
    
    ${mediaQueries.minTablet} {
      .responsive-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-desktop-md);
      }
    }
    
    ${mediaQueries.minDesktop} {
      .responsive-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-desktop-lg);
      }
    }
  `,
  
  /**
   * Typography pattern - responsive text sizing
   */
  typography: `
    .responsive-text {
      font-size: var(--text-mobile-base);
      line-height: 1.5;
    }
    
    ${mediaQueries.minTablet} {
      .responsive-text {
        font-size: var(--text-desktop-base);
        line-height: 1.6;
      }
    }
    
    .responsive-heading {
      font-size: var(--text-mobile-2xl);
      line-height: 1.2;
    }
    
    ${mediaQueries.minTablet} {
      .responsive-heading {
        font-size: var(--text-desktop-3xl);
        line-height: 1.3;
      }
    }
    
    ${mediaQueries.minDesktop} {
      .responsive-heading {
        font-size: var(--text-desktop-4xl);
      }
    }
  `,
  
  /**
   * Spacing pattern - responsive margins and padding
   */
  spacing: `
    .responsive-section {
      padding: var(--spacing-mobile-xl) 0;
    }
    
    ${mediaQueries.minTablet} {
      .responsive-section {
        padding: var(--spacing-desktop-xl) 0;
      }
    }
    
    ${mediaQueries.minDesktop} {
      .responsive-section {
        padding: var(--spacing-desktop-2xl) 0;
      }
    }
  `,
  
  /**
   * Touch-friendly patterns
   */
  touch: `
    .touch-target {
      min-height: 44px;
      min-width: 44px;
      padding: 12px;
    }
    
    ${mediaQueries.hover} {
      .touch-target:hover {
        background-color: var(--color-hover);
      }
    }
    
    ${mediaQueries.touch} {
      .touch-target {
        min-height: 48px;
        min-width: 48px;
        padding: 16px;
      }
    }
  `
} as const;

/**
 * Performance-optimized CSS utilities
 */
export const performanceOptimizedCSS = `
  /* Use CSS containment for better performance */
  .responsive-container {
    contain: layout style;
  }
  
  /* Optimize animations for reduced motion */
  ${mediaQueries.reducedMotion} {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* GPU acceleration for smooth animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  /* Optimize for high DPI displays */
  ${mediaQueries.retina} {
    .retina-optimized {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }
`;

/**
 * Guidelines for when to use CSS vs JavaScript
 */
export const responsiveGuidelines = {
  useCSSFor: [
    'Layout changes (grid, flexbox)',
    'Typography scaling',
    'Spacing adjustments',
    'Color scheme changes',
    'Simple show/hide patterns',
    'Basic animations',
    'Print styles',
    'Accessibility preferences'
  ],
  
  useJavaScriptFor: [
    'Complex conditional logic',
    'Dynamic content loading',
    'Touch gesture handling',
    'Viewport calculations',
    'Device-specific API access',
    'Performance optimizations',
    'Complex state management',
    'Real-time responsive adjustments'
  ],
  
  hybridApproaches: [
    'Use CSS for base styles, JavaScript for enhancements',
    'CSS custom properties controlled by JavaScript',
    'Progressive enhancement patterns',
    'Intersection Observer for lazy loading',
    'ResizeObserver for specific component needs'
  ]
} as const;

/**
 * Utility to generate CSS custom properties
 */
export function generateCSSCustomProperties(): string {
  return Object.entries(cssCustomProperties)
    .map(([property, value]) => `  ${property}: ${value};`)
    .join('\n');
}

/**
 * Utility to generate all responsive patterns
 */
export function generateResponsiveCSS(): string {
  return [
    ':root {',
    generateCSSCustomProperties(),
    '}',
    '',
    Object.values(responsivePatterns).join('\n\n'),
    '',
    performanceOptimizedCSS
  ].join('\n');
}
