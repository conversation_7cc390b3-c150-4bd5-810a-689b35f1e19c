/**
 * Type definitions for the modular route system
 */

import { ComponentType } from 'react';

// Route loader function type
export type RouteLoader = () => Promise<{ default: ComponentType<Record<string, unknown>> }>;

// Route configuration interface
export interface RouteConfig {
  path: string;
  element: React.ReactNode | null;
  children?: RouteConfig[];
}

// Route module interface
export interface RouteModule {
  name: string;
  category: 'core' | 'patient-resources' | 'expertise' | 'locations' | 'gp-resources';
  routes: Record<string, RouteLoader>;
  priority: 'high' | 'medium' | 'low';
  preload?: string[]; // Routes to preload for this module
}

// Route registry interface
export interface RouteRegistry {
  modules: Map<string, RouteModule>;
  routes: Map<string, RouteLoader>;
  preloadedRoutes: Set<string>;
}

// Route metadata interface (extended from existing)
export interface RouteMetadata {
  title: string;
  description: string;
  keywords?: string[];
  category: 'core' | 'patient-resources' | 'expertise' | 'locations' | 'gp-resources';
  priority: 'high' | 'medium' | 'low';
  changeFreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  module?: string; // Which module this route belongs to
}

// Route validation result
export interface RouteValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingRoutes: string[];
  duplicateRoutes: string[];
}

// Lazy loading options
export interface LazyLoadingOptions {
  retryAttempts?: number;
  retryDelay?: number;
  fallback?: ComponentType;
  errorFallback?: ComponentType<{ error: Error }>;
}
