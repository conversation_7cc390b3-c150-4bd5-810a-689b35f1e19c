/**
 * Enhanced route loader with improved lazy loading, error handling, and performance optimization
 */

import React, { ComponentType, Suspense, useEffect, useState } from 'react';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Loading } from '@/components/Loading';
import { logRoute } from '@/lib/dev-console';
import { RouteLoader, LazyLoadingOptions } from './modules/types';

// Enhanced loading fallback component
const EnhancedLoadingFallback: React.FC<{ 
  routePath?: string;
  loadingTime?: number;
}> = ({ routePath, loadingTime = 0 }) => {
  const [showSlowLoadingMessage, setShowSlowLoadingMessage] = useState(false);

  useEffect(() => {
    // Show slow loading message after 3 seconds
    const timer = setTimeout(() => {
      setShowSlowLoadingMessage(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-[200px] p-8">
      <Loading />
      <div className="mt-4 text-center">
        <p className="text-muted-foreground">Loading page...</p>
        {showSlowLoadingMessage && (
          <p className="text-sm text-muted-foreground mt-2">
            This is taking longer than usual. Please wait...
          </p>
        )}
        {process.env.NODE_ENV === 'development' && routePath && (
          <p className="text-xs text-muted-foreground mt-1">
            Route: {routePath}
          </p>
        )}
      </div>
    </div>
  );
};

// Enhanced error fallback component
const EnhancedErrorFallback: React.FC<{ 
  error: Error;
  routePath?: string;
  onRetry?: () => void;
}> = ({ error, routePath, onRetry }) => {
  const [retryCount, setRetryCount] = useState(0);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    onRetry?.();
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-8 text-center">
      <div className="max-w-md">
        <h2 className="text-xl font-semibold text-destructive mb-4">
          Failed to Load Page
        </h2>
        <p className="text-muted-foreground mb-6">
          We encountered an error while loading this page. This might be due to a network issue or a temporary problem.
        </p>
        
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-4 bg-muted rounded-lg text-left">
            <p className="text-sm font-mono text-destructive">
              {error.message}
            </p>
            {routePath && (
              <p className="text-xs text-muted-foreground mt-2">
                Route: {routePath}
              </p>
            )}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            disabled={retryCount >= 3}
          >
            {retryCount >= 3 ? 'Max Retries Reached' : `Retry ${retryCount > 0 ? `(${retryCount})` : ''}`}
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 border border-border rounded-md hover:bg-muted transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Enhanced lazy component creator with improved error handling and performance
 */
export function createEnhancedLazyComponent<T extends ComponentType<Record<string, unknown>>>(
  importFunc: RouteLoader,
  options: LazyLoadingOptions & { routePath?: string } = {}
): ComponentType<React.ComponentProps<T>> {
  const {
    retryAttempts = 3,
    retryDelay = 1000,
    routePath,
    fallback = () => <EnhancedLoadingFallback routePath={routePath} />,
    errorFallback = (props) => <EnhancedErrorFallback {...props} routePath={routePath} />
  } = options;

  // Track loading performance
  const loadingStartTime = performance.now();

  const LazyComponent = React.lazy(async () => {
    let attempts = 0;
    
    const loadWithRetry = async (): Promise<{ default: T }> => {
      try {
        const startTime = performance.now();
        const result = await importFunc();
        const loadTime = performance.now() - startTime;
        
        // Log performance in development
        if (process.env.NODE_ENV === 'development') {
          logRoute(`Route loaded: ${routePath || 'unknown'} in ${loadTime.toFixed(2)}ms`);
        }
        
        return result;
      } catch (error) {
        attempts++;
        
        // Log retry attempts
        if (process.env.NODE_ENV === 'development') {
          logRoute(`Route loading failed (attempt ${attempts}): ${routePath || 'unknown'}`, error);
        }
        
        if (attempts < retryAttempts) {
          // Exponential backoff with jitter
          const delay = retryDelay * Math.pow(2, attempts - 1) + Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
          return loadWithRetry();
        }
        
        // Final failure
        logRoute(`Route loading failed permanently: ${routePath || 'unknown'}`, error);
        throw error;
      }
    };
    
    return loadWithRetry();
  });

  return (props: React.ComponentProps<T>) => {
    const [key, setKey] = useState(0);

    const handleRetry = () => {
      setKey(prev => prev + 1);
    };

    return (
      <ErrorBoundary 
        key={key}
        fallback={(error) => errorFallback({ error, onRetry: handleRetry })}
      >
        <Suspense fallback={React.createElement(fallback)}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };
}

/**
 * Enhanced route preloader with intelligent preloading strategies
 */
export class EnhancedRoutePreloader {
  private static instance: EnhancedRoutePreloader;
  private preloadedRoutes = new Set<string>();
  private preloadingRoutes = new Set<string>();
  private preloadQueue: Array<{ path: string; loader: RouteLoader; priority: number }> = [];
  private isProcessingQueue = false;

  static getInstance(): EnhancedRoutePreloader {
    if (!EnhancedRoutePreloader.instance) {
      EnhancedRoutePreloader.instance = new EnhancedRoutePreloader();
    }
    return EnhancedRoutePreloader.instance;
  }

  /**
   * Preload a route with priority
   */
  preloadRoute(path: string, loader: RouteLoader, priority: number = 1): void {
    if (this.preloadedRoutes.has(path) || this.preloadingRoutes.has(path)) {
      return; // Already preloaded or preloading
    }

    // Add to queue
    this.preloadQueue.push({ path, loader, priority });
    this.preloadQueue.sort((a, b) => b.priority - a.priority); // Higher priority first

    this.processQueue();
  }

  /**
   * Preload multiple routes
   */
  preloadRoutes(routes: Array<{ path: string; loader: RouteLoader; priority?: number }>): void {
    routes.forEach(({ path, loader, priority = 1 }) => {
      this.preloadRoute(path, loader, priority);
    });
  }

  /**
   * Process the preload queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.preloadQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.preloadQueue.length > 0) {
      const { path, loader } = this.preloadQueue.shift()!;
      
      if (this.preloadedRoutes.has(path) || this.preloadingRoutes.has(path)) {
        continue;
      }

      this.preloadingRoutes.add(path);

      try {
        // Use requestIdleCallback if available, otherwise setTimeout
        await this.schedulePreload(() => this.executePreload(path, loader));
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          logRoute(`Failed to preload route: ${path}`, error);
        }
      } finally {
        this.preloadingRoutes.delete(path);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Schedule preload using idle time
   */
  private schedulePreload(callback: () => Promise<void>): Promise<void> {
    return new Promise((resolve) => {
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        (window as any).requestIdleCallback(async () => {
          await callback();
          resolve();
        });
      } else {
        setTimeout(async () => {
          await callback();
          resolve();
        }, 100);
      }
    });
  }

  /**
   * Execute the actual preload
   */
  private async executePreload(path: string, loader: RouteLoader): Promise<void> {
    try {
      await loader();
      this.preloadedRoutes.add(path);
      
      if (process.env.NODE_ENV === 'development') {
        logRoute(`Successfully preloaded route: ${path}`);
      }
    } catch (error) {
      // Silently fail preloading - it's not critical
      if (process.env.NODE_ENV === 'development') {
        logRoute(`Preload failed for route: ${path}`, error);
      }
    }
  }

  /**
   * Check if a route is preloaded
   */
  isPreloaded(path: string): boolean {
    return this.preloadedRoutes.has(path);
  }

  /**
   * Get preload statistics
   */
  getStatistics() {
    return {
      preloadedCount: this.preloadedRoutes.size,
      preloadingCount: this.preloadingRoutes.size,
      queueLength: this.preloadQueue.length
    };
  }
}

// Export singleton instance
export const routePreloader = EnhancedRoutePreloader.getInstance();

/**
 * Hook for intelligent route preloading on hover/focus
 */
export function useEnhancedRoutePreloader() {
  const preloader = EnhancedRoutePreloader.getInstance();

  const preloadOnInteraction = React.useCallback((path: string, loader: RouteLoader, priority: number = 1) => {
    return {
      onMouseEnter: () => preloader.preloadRoute(path, loader, priority),
      onFocus: () => preloader.preloadRoute(path, loader, priority),
      onTouchStart: () => preloader.preloadRoute(path, loader, priority + 1) // Higher priority for touch
    };
  }, [preloader]);

  return { preloadOnInteraction, preloader };
}
