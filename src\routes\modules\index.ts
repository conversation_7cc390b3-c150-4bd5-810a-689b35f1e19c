/**
 * Modular route system exports
 * Central export point for all route modules and utilities
 */

// Type definitions
export type {
  RouteLoader,
  RouteConfig,
  RouteModule,
  RouteRegistry,
  RouteMetadata,
  RouteValidationResult,
  LazyLoadingOptions
} from './types';

// Route modules
export { coreRoutesModule, coreRoutePaths, coreRouteMetadata } from './core';
export { patientResourcesModule, patientResourcesRoutePaths, patientResourcesRouteMetadata } from './patient-resources';
export { conditionsModule, conditionsRoutePaths, conditionsRouteMetadata } from './conditions';
export { expertiseModule, expertiseRoutePaths, expertiseRouteMetadata } from './expertise';
export { locationsModule, locationsRoutePaths, locationsRouteMetadata } from './locations';
export { gpResourcesModule, gpResourcesRoutePaths, gpResourcesRouteMetadata } from './gp-resources';

// Registry system
export {
  ModularRouteRegistry,
  routeRegistry,
  getAllRoutes,
  getRoutesByCategory,
  getRouteLoader,
  hasRoute,
  validateRoutes,
  getAllRouteMetadata,
  getHighPriorityRoutes,
  getRouteRedirects
} from './registry';

// Convenience exports for backward compatibility
export { ROUTE_PATHS, ROUTE_REDIRECTS, SUPPORTED_LANGUAGES } from '../route-definitions';
