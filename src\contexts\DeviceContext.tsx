import React, { createContext, useContext, useState, useEffect, useMemo, useCallback, useRef, ReactNode } from 'react';

import ErrorBoundary from '@/components/ErrorBoundary';
import { BREAKPOINTS, DEBOUNCE_DELAY } from '@/lib/constants';

/* eslint-disable react-refresh/only-export-components */
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  hasHover: boolean;
  orientation: 'portrait' | 'landscape';
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'unknown';
  width: number;
  height: number;
}

// Performance monitoring interface
interface DeviceContextPerformance {
  renderCount: number;
  lastUpdate: number;
  updateFrequency: number;
}

interface DeviceContextType {
  deviceInfo: DeviceInfo;
  isLoaded: boolean;
  performance: DeviceContextPerformance;
}

const DeviceContext = createContext<DeviceContextType | undefined>(undefined);

interface DeviceProviderProps {
  children: ReactNode;
  debounceDelay?: number;
  throttleDelay?: number;
  enablePerformanceMonitoring?: boolean;
}

/**
 * Enhanced Device Detection Context Provider
 * Provides optimized device detection with advanced performance optimizations
 */
export function DeviceProvider({
  children,
  debounceDelay = DEBOUNCE_DELAY.RESIZE,
  throttleDelay = 16, // 60fps
  enablePerformanceMonitoring = process.env.NODE_ENV === 'development'
}: DeviceProviderProps) {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isTouchDevice: false,
    hasHover: false,
    orientation: 'portrait',
    screenSize: 'unknown',
    width: 0,
    height: 0
  });
  const [isLoaded, setIsLoaded] = useState(false);

  // Performance monitoring state
  const performanceRef = useRef<DeviceContextPerformance>({
    renderCount: 0,
    lastUpdate: 0,
    updateFrequency: 0
  });

  // Refs for throttling and debouncing
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const throttleTimeoutRef = useRef<NodeJS.Timeout>();
  const lastThrottleTimeRef = useRef<number>(0);

  // Enhanced device detection with performance optimizations
  const detectDeviceInfo = useCallback(() => {
    if (typeof window === 'undefined') return null;

    const width = window.innerWidth;
    const height = window.innerHeight;

    // Determine device type
    const isMobile = width < BREAKPOINTS.MOBILE;
    const isTablet = width >= BREAKPOINTS.MOBILE && width < BREAKPOINTS.TABLET;
    const isDesktop = width >= BREAKPOINTS.TABLET;

    // Detect touch capability (cached for performance)
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Detect hover capability (cached for performance)
    const hasHover = window.matchMedia('(hover: hover)').matches;

    // Determine orientation
    const orientation = height > width ? 'portrait' : 'landscape';

    // Determine screen size category
    let screenSize: DeviceInfo['screenSize'] = 'unknown';
    if (width < 475) screenSize = 'xs';
    else if (width < 640) screenSize = 'sm';
    else if (width < 768) screenSize = 'md';
    else if (width < 1024) screenSize = 'lg';
    else if (width < 1280) screenSize = 'xl';
    else screenSize = '2xl';

    return {
      isMobile,
      isTablet,
      isDesktop,
      isTouchDevice,
      hasHover,
      orientation,
      screenSize,
      width,
      height
    };
  }, []);

  // Throttled update function
  const throttledUpdate = useCallback(() => {
    const now = Date.now();

    if (now - lastThrottleTimeRef.current >= throttleDelay) {
      lastThrottleTimeRef.current = now;

      const newDeviceInfo = detectDeviceInfo();
      if (!newDeviceInfo) return;

      // Performance monitoring
      if (enablePerformanceMonitoring) {
        performanceRef.current.renderCount++;
        const timeSinceLastUpdate = now - performanceRef.current.lastUpdate;
        performanceRef.current.updateFrequency = timeSinceLastUpdate;
        performanceRef.current.lastUpdate = now;
      }

      // Only update if values have actually changed (deep comparison for performance)
      setDeviceInfo(prevInfo => {
        const hasChanged = Object.keys(newDeviceInfo).some(
          key => prevInfo[key as keyof DeviceInfo] !== newDeviceInfo[key as keyof DeviceInfo]
        );

        return hasChanged ? newDeviceInfo : prevInfo;
      });

      if (!isLoaded) {
        setIsLoaded(true);
      }
    } else {
      // Schedule throttled update
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }

      throttleTimeoutRef.current = setTimeout(() => {
        throttledUpdate();
      }, throttleDelay - (now - lastThrottleTimeRef.current));
    }
  }, [detectDeviceInfo, throttleDelay, enablePerformanceMonitoring, isLoaded]);

  // Debounced resize handler
  const debouncedDetectDevice = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      throttledUpdate();
    }, debounceDelay);
  }, [throttledUpdate, debounceDelay]);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initial detection (immediate, no debouncing)
    throttledUpdate();

    // Listen for resize and orientation changes with debouncing
    window.addEventListener('resize', debouncedDetectDevice, { passive: true });
    window.addEventListener('orientationchange', debouncedDetectDevice, { passive: true });

    // Cleanup function
    return () => {
      window.removeEventListener('resize', debouncedDetectDevice);
      window.removeEventListener('orientationchange', debouncedDetectDevice);

      // Clear any pending timeouts
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [debouncedDetectDevice, throttledUpdate]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    deviceInfo,
    isLoaded,
    performance: enablePerformanceMonitoring ? performanceRef.current : {
      renderCount: 0,
      lastUpdate: 0,
      updateFrequency: 0
    }
  }), [deviceInfo, isLoaded, enablePerformanceMonitoring]);

  return (
    <ErrorBoundary>
      <DeviceContext.Provider value={contextValue}>
      {children}
    </DeviceContext.Provider>
    </ErrorBoundary>
  );
}

/**
 * Hook to access device information
 * Returns memoized device info to prevent unnecessary re-renders
 */
export function useDeviceDetection(): DeviceInfo {
  const context = useContext(DeviceContext);
  
  if (context === undefined) {
    throw new Error('useDeviceDetection must be used within a DeviceProvider');
  }

  return context.deviceInfo;
}

/**
 * Hook to check if device detection is loaded
 * Useful for preventing hydration mismatches in SSR
 */
export function useDeviceLoaded(): boolean {
  const context = useContext(DeviceContext);
  
  if (context === undefined) {
    throw new Error('useDeviceLoaded must be used within a DeviceProvider');
  }

  return context.isLoaded;
}

/**
 * Higher-order component for device-specific rendering
 */
export function withDeviceDetection<P extends object>(
  Component: React.ComponentType<P & { deviceInfo: DeviceInfo }>
) {
  const WrappedComponent = (props: P) => {
    const deviceInfo = useDeviceDetection();
    
    return <Component {...props} deviceInfo={deviceInfo} />;
  };

  WrappedComponent.displayName = `withDeviceDetection(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Selective Device Detection Hooks
 * These hooks only subscribe to specific device properties to prevent unnecessary re-renders
 */

/**
 * Hook for mobile-only detection (optimized for common use case)
 */
export function useIsMobile(): boolean {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useIsMobile must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.isMobile, [context.deviceInfo.isMobile]);
}

/**
 * Hook for tablet-only detection
 */
export function useIsTablet(): boolean {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useIsTablet must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.isTablet, [context.deviceInfo.isTablet]);
}

/**
 * Hook for desktop-only detection
 */
export function useIsDesktop(): boolean {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useIsDesktop must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.isDesktop, [context.deviceInfo.isDesktop]);
}

/**
 * Hook for touch device detection
 */
export function useIsTouchDevice(): boolean {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useIsTouchDevice must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.isTouchDevice, [context.deviceInfo.isTouchDevice]);
}

/**
 * Hook for hover capability detection
 */
export function useHasHover(): boolean {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useHasHover must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.hasHover, [context.deviceInfo.hasHover]);
}

/**
 * Hook for orientation detection
 */
export function useOrientation(): 'portrait' | 'landscape' {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useOrientation must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.orientation, [context.deviceInfo.orientation]);
}

/**
 * Hook for screen size category detection
 */
export function useScreenSize(): DeviceInfo['screenSize'] {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useScreenSize must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.screenSize, [context.deviceInfo.screenSize]);
}

/**
 * Hook for viewport dimensions (width and height)
 */
export function useViewportSize(): { width: number; height: number } {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useViewportSize must be used within a DeviceProvider');
  }

  return useMemo(() => ({
    width: context.deviceInfo.width,
    height: context.deviceInfo.height
  }), [context.deviceInfo.width, context.deviceInfo.height]);
}

/**
 * Hook for viewport width only
 */
export function useViewportWidth(): number {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useViewportWidth must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.width, [context.deviceInfo.width]);
}

/**
 * Hook for viewport height only
 */
export function useViewportHeight(): number {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useViewportHeight must be used within a DeviceProvider');
  }

  return useMemo(() => context.deviceInfo.height, [context.deviceInfo.height]);
}

/**
 * Hook for breakpoint-based detection
 */
export function useBreakpoint(): 'mobile' | 'tablet' | 'desktop' {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useBreakpoint must be used within a DeviceProvider');
  }

  return useMemo(() => {
    if (context.deviceInfo.isMobile) return 'mobile';
    if (context.deviceInfo.isTablet) return 'tablet';
    return 'desktop';
  }, [context.deviceInfo.isMobile, context.deviceInfo.isTablet]);
}

/**
 * Hook for responsive value selection based on breakpoint
 */
export function useResponsiveValue<T>(values: {
  mobile: T;
  tablet?: T;
  desktop: T;
}): T {
  const breakpoint = useBreakpoint();

  return useMemo(() => {
    switch (breakpoint) {
      case 'mobile':
        return values.mobile;
      case 'tablet':
        return values.tablet ?? values.desktop;
      case 'desktop':
        return values.desktop;
      default:
        return values.desktop;
    }
  }, [breakpoint, values]);
}

/**
 * Hook for device performance monitoring (development only)
 */
export function useDevicePerformance(): DeviceContextPerformance | null {
  const context = useContext(DeviceContext);

  if (context === undefined) {
    throw new Error('useDevicePerformance must be used within a DeviceProvider');
  }

  return useMemo(() => {
    return process.env.NODE_ENV === 'development' ? context.performance : null;
  }, [context.performance]);
}

/**
 * Utility hook for responsive breakpoint detection
 */
export function useBreakpoint(): DeviceInfo['screenSize'] {
  const { screenSize } = useDeviceDetection();
  return screenSize;
}

export default DeviceContext;