/**
 * Medical Conditions routes module
 * Contains all specific medical condition pages
 */

import { RouteModule } from './types';
import { ROUTE_PATHS } from '../route-definitions';

export const conditionsModule: RouteModule = {
  name: 'conditions',
  category: 'patient-resources',
  priority: 'medium',
  preload: [
    ROUTE_PATHS.CONDITIONS.HERNIATED_DISC,
    ROUTE_PATHS.CONDITIONS.SPINAL_STENOSIS,
    ROUTE_PATHS.CONDITIONS.SCIATICA
  ],
  routes: {
    // Spine conditions
    [ROUTE_PATHS.CONDITIONS.HERNIATED_DISC]: () => import('@/pages/patient-resources/conditions/HerniatedDisc'),
    [ROUTE_PATHS.CONDITIONS.SPINAL_STENOSIS]: () => import('@/pages/patient-resources/conditions/SpinalStenosis'),
    [ROUTE_PATHS.CONDITIONS.SCIATICA]: () => import('@/pages/patient-resources/conditions/Sciatica'),
    [ROUTE_PATHS.CONDITIONS.RADICULOPATHY]: () => import('@/pages/patient-resources/conditions/Radiculopathy'),
    [ROUTE_PATHS.CONDITIONS.ARTHROSIS]: () => import('@/pages/patient-resources/conditions/Arthrosis'),
    [ROUTE_PATHS.CONDITIONS.DISCOPATHY]: () => import('@/pages/patient-resources/conditions/Discopathy'),
    [ROUTE_PATHS.CONDITIONS.FACET_ARTHROPATHY]: () => import('@/pages/patient-resources/conditions/FacetArthropathy'),
    [ROUTE_PATHS.CONDITIONS.SACROILIAC_ARTHROPATHY]: () => import('@/pages/patient-resources/conditions/SacroiliacArthropathy'),
    [ROUTE_PATHS.CONDITIONS.SPONDYLOSIS]: () => import('@/pages/patient-resources/conditions/Spondylosis'),
    [ROUTE_PATHS.CONDITIONS.PARS_DEFECTS]: () => import('@/pages/patient-resources/conditions/ParsDefects'),
    [ROUTE_PATHS.CONDITIONS.SPONDYLOLISTHESIS]: () => import('@/pages/patient-resources/conditions/Spondylolisthesis'),
    [ROUTE_PATHS.CONDITIONS.FACET_JOINT_SYNDROME]: () => import('@/pages/patient-resources/conditions/FacetArthropathy'),
    [ROUTE_PATHS.CONDITIONS.CERVICAL_DISC_HERNIATION]: () => import('@/pages/patient-resources/conditions/HerniatedDisc'),
    [ROUTE_PATHS.CONDITIONS.WHIPLASH]: () => import('@/pages/patient-resources/conditions/Radiculopathy'),
    [ROUTE_PATHS.CONDITIONS.THORACIC_COMPRESSION_FRACTURE]: () => import('@/pages/patient-resources/conditions/SpinalStenosis'),
    [ROUTE_PATHS.CONDITIONS.CERVICAL_MYELOPATHY]: () => import('@/pages/patient-resources/conditions/CervicalMyelopathy'),
    [ROUTE_PATHS.CONDITIONS.CAUDA_EQUINA_SYNDROME]: () => import('@/pages/patient-resources/conditions/CaudaEquinaSyndrome'),

    // Brain conditions
    [ROUTE_PATHS.CONDITIONS.BRAIN_TUMOUR]: () => import('@/pages/patient-resources/conditions/BrainTumour'),
    [ROUTE_PATHS.CONDITIONS.TRIGEMINAL_NEURALGIA]: () => import('@/pages/patient-resources/conditions/TrigeminalNeuralgia'),
    [ROUTE_PATHS.CONDITIONS.CEREBRAL_ANEURYSM]: () => import('@/pages/patient-resources/conditions/CerebralAneurysm'),
    [ROUTE_PATHS.CONDITIONS.CEREBRAL_MENINGIOMA]: () => import('@/pages/patient-resources/conditions/CerebralMeningioma'),
    [ROUTE_PATHS.CONDITIONS.CEREBRAL_CAVERNOMA]: () => import('@/pages/patient-resources/conditions/CerebralCavernoma'),
    [ROUTE_PATHS.CONDITIONS.CEREBRAL_AVM]: () => import('@/pages/patient-resources/conditions/CerebralAVM'),
    [ROUTE_PATHS.CONDITIONS.HEMIFACIAL_SPASM]: () => import('@/pages/patient-resources/conditions/HemifacialSpasm'),
    [ROUTE_PATHS.CONDITIONS.CHIARI_MALFORMATION]: () => import('@/pages/patient-resources/conditions/ChiariMalformation'),
    [ROUTE_PATHS.CONDITIONS.HYDROCEPHALUS]: () => import('@/pages/patient-resources/conditions/Hydrocephalus'),

    // Peripheral nerve conditions
    [ROUTE_PATHS.CONDITIONS.CARPAL_TUNNEL_SYNDROME]: () => import('@/pages/patient-resources/conditions/CarpalTunnelSyndrome'),
    [ROUTE_PATHS.CONDITIONS.ULNAR_NEUROPATHY]: () => import('@/pages/patient-resources/conditions/UlnarNeuropathy'),
    [ROUTE_PATHS.CONDITIONS.MERALGIA_PARESTHETICA]: () => import('@/pages/patient-resources/conditions/MeralgiaParesthetica'),
    [ROUTE_PATHS.CONDITIONS.TARSAL_TUNNEL_SYNDROME]: () => import('@/pages/patient-resources/conditions/TarsalTunnelSyndrome'),
    [ROUTE_PATHS.CONDITIONS.PERONEAL_NERVE_PALSY]: () => import('@/pages/patient-resources/conditions/PeronealNervePalsy'),
    [ROUTE_PATHS.CONDITIONS.PERIPHERAL_NERVE_TUMORS]: () => import('@/pages/patient-resources/conditions/PeripheralNerveTumors'),
    [ROUTE_PATHS.CONDITIONS.PIRIFORMIS_SYNDROME]: () => import('@/pages/patient-resources/conditions/PiriformisSyndrome'),
    [ROUTE_PATHS.CONDITIONS.THORACIC_OUTLET_SYNDROME]: () => import('@/pages/patient-resources/conditions/ThoracicOutletSyndrome'),
    [ROUTE_PATHS.CONDITIONS.OCCIPITAL_NEURALGIA]: () => import('@/pages/patient-resources/conditions/OccipitalNeuralgia'),
  }
};

// Export route paths for this module
export const conditionsRoutePaths = Object.keys(conditionsModule.routes);

// Export metadata for condition routes
export const conditionsRouteMetadata = {
  [ROUTE_PATHS.CONDITIONS.BRAIN_TUMOUR]: {
    title: 'Brain Tumour: Comprehensive Patient Guide',
    description: 'Complete guide to brain tumours: types, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques.',
    keywords: ['brain tumour', 'brain tumor', 'glioma', 'meningioma', 'neurosurgery', 'brain surgery', 'image-guided surgery', 'brain cancer treatment'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'conditions'
  },
  [ROUTE_PATHS.CONDITIONS.TRIGEMINAL_NEURALGIA]: {
    title: 'Trigeminal Neuralgia: Comprehensive Patient Guide',
    description: 'Complete guide to trigeminal neuralgia: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and pain management.',
    keywords: ['trigeminal neuralgia', 'facial pain', 'neuralgia', 'microvascular decompression', 'gamma knife', 'carbamazepine', 'facial nerve pain', 'tic douloureux'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'conditions'
  },
  [ROUTE_PATHS.CONDITIONS.CEREBRAL_ANEURYSM]: {
    title: 'Cerebral Aneurysm: Comprehensive Patient Guide',
    description: 'Complete guide to cerebral aneurysms: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical and endovascular techniques.',
    keywords: ['cerebral aneurysm', 'brain aneurysm', 'aneurysm clipping', 'endovascular coiling', 'subarachnoid haemorrhage', 'neurosurgery', 'flow diverter', 'aneurysm rupture'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'conditions'
  },
  [ROUTE_PATHS.CONDITIONS.CAUDA_EQUINA_SYNDROME]: {
    title: 'Cauda Equina Syndrome: Emergency Patient Guide',
    description: 'Emergency guide to cauda equina syndrome: causes, symptoms, diagnosis, and urgent treatment options. Expert neurosurgical care with emergency surgical intervention and comprehensive rehabilitation.',
    keywords: ['cauda equina syndrome', 'spinal emergency', 'bladder dysfunction', 'saddle anaesthesia', 'emergency surgery', 'spinal decompression', 'neurosurgery', 'spinal cord'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'conditions'
  },
  [ROUTE_PATHS.CONDITIONS.CERVICAL_MYELOPATHY]: {
    title: 'Cervical Myelopathy: Comprehensive Patient Guide',
    description: 'Comprehensive guide to cervical myelopathy: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive management for optimal neurological outcomes.',
    keywords: ['cervical myelopathy', 'spinal cord compression', 'cervical stenosis', 'ACDF', 'laminectomy', 'neurosurgery', 'spinal surgery', 'neck surgery'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'conditions'
  }
};
