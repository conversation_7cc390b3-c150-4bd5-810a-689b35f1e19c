/**
 * Integration test for the modular route system
 * Tests all components working together
 */

import { routeRegistry } from './modules';
import { validateAllRoutes, testAllRoutes, runCompleteRouteValidation } from './validation';
import { metadataManager } from './metadata';
import { routePreloader } from './enhanced-route-loader';

export interface IntegrationTestResult {
  timestamp: string;
  success: boolean;
  summary: {
    totalRoutes: number;
    validRoutes: number;
    testedRoutes: number;
    metadataRoutes: number;
    preloadedRoutes: number;
  };
  details: {
    moduleValidation: boolean;
    routeValidation: boolean;
    routeTesting: boolean;
    metadataValidation: boolean;
    preloadingTest: boolean;
  };
  errors: string[];
  warnings: string[];
  performance: {
    validationTime: number;
    testingTime: number;
    totalTime: number;
  };
}

/**
 * Run comprehensive integration test
 */
export async function runIntegrationTest(): Promise<IntegrationTestResult> {
  const startTime = Date.now();
  console.log('🚀 Starting modular route system integration test...');

  const result: IntegrationTestResult = {
    timestamp: new Date().toISOString(),
    success: false,
    summary: {
      totalRoutes: 0,
      validRoutes: 0,
      testedRoutes: 0,
      metadataRoutes: 0,
      preloadedRoutes: 0
    },
    details: {
      moduleValidation: false,
      routeValidation: false,
      routeTesting: false,
      metadataValidation: false,
      preloadingTest: false
    },
    errors: [],
    warnings: [],
    performance: {
      validationTime: 0,
      testingTime: 0,
      totalTime: 0
    }
  };

  try {
    // Test 1: Module System Validation
    console.log('📋 Testing module system...');
    const moduleTest = await testModuleSystem();
    result.details.moduleValidation = moduleTest.success;
    if (!moduleTest.success) {
      result.errors.push(...moduleTest.errors);
    }
    result.warnings.push(...moduleTest.warnings);

    // Test 2: Route Validation
    console.log('🔍 Running route validation...');
    const validationStart = Date.now();
    const validationResult = await runCompleteRouteValidation();
    result.performance.validationTime = Date.now() - validationStart;
    
    result.details.routeValidation = validationResult.summary.allValid;
    result.details.routeTesting = validationResult.summary.allTested;
    
    if (!validationResult.summary.allValid) {
      result.errors.push('Route validation failed');
    }
    if (!validationResult.summary.allTested) {
      result.errors.push('Route testing failed');
    }

    // Test 3: Metadata System
    console.log('📊 Testing metadata system...');
    const metadataTest = await testMetadataSystem();
    result.details.metadataValidation = metadataTest.success;
    if (!metadataTest.success) {
      result.errors.push(...metadataTest.errors);
    }
    result.warnings.push(...metadataTest.warnings);

    // Test 4: Preloading System
    console.log('⚡ Testing preloading system...');
    const preloadTest = await testPreloadingSystem();
    result.details.preloadingTest = preloadTest.success;
    if (!preloadTest.success) {
      result.errors.push(...preloadTest.errors);
    }

    // Collect summary data
    result.summary.totalRoutes = routeRegistry.getAllRoutes().length;
    result.summary.validRoutes = validationResult.fullValidation.summary.validRoutes;
    result.summary.testedRoutes = validationResult.testing.passed;
    result.summary.metadataRoutes = Object.keys(metadataManager.getAllMetadata()).length;
    result.summary.preloadedRoutes = routePreloader.getStatistics().preloadedCount;

    // Determine overall success
    result.success = Object.values(result.details).every(test => test);

    result.performance.totalTime = Date.now() - startTime;

    // Log results
    if (result.success) {
      console.log('✅ Integration test PASSED');
    } else {
      console.log('❌ Integration test FAILED');
      console.log('Errors:', result.errors);
    }

    console.log('📊 Summary:', result.summary);
    console.log('⏱️ Performance:', result.performance);

  } catch (error) {
    result.errors.push(`Integration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    result.performance.totalTime = Date.now() - startTime;
  }

  return result;
}

/**
 * Test module system functionality
 */
async function testModuleSystem(): Promise<{ success: boolean; errors: string[]; warnings: string[] }> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Test module registration
    const modules = routeRegistry.getModules();
    if (modules.length === 0) {
      errors.push('No modules registered');
    }

    // Test each module
    for (const module of modules) {
      if (Object.keys(module.routes).length === 0) {
        warnings.push(`Module ${module.name} has no routes`);
      }

      // Test module validation
      const validation = routeRegistry.validateRoutes();
      if (!validation.isValid) {
        errors.push(`Module validation failed: ${validation.errors.join(', ')}`);
      }
    }

    // Test route retrieval
    const allRoutes = routeRegistry.getAllRoutes();
    if (allRoutes.length === 0) {
      errors.push('No routes found in registry');
    }

    // Test category filtering
    const coreRoutes = routeRegistry.getRoutesByCategory('core');
    if (coreRoutes.length === 0) {
      warnings.push('No core routes found');
    }

  } catch (error) {
    errors.push(`Module system test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    success: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Test metadata system functionality
 */
async function testMetadataSystem(): Promise<{ success: boolean; errors: string[]; warnings: string[] }> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Test metadata retrieval
    const allMetadata = metadataManager.getAllMetadata();
    if (Object.keys(allMetadata).length === 0) {
      errors.push('No metadata found');
    }

    // Test metadata validation
    const allRoutes = routeRegistry.getAllRoutes();
    const metadataValidation = metadataManager.validateMetadata(allRoutes);
    
    if (!metadataValidation.isValid) {
      errors.push(...metadataValidation.errors);
    }
    warnings.push(...metadataValidation.warnings);

    // Test sitemap generation
    const sitemapData = metadataManager.generateSitemapData();
    if (sitemapData.length === 0) {
      errors.push('Sitemap generation failed');
    }

    // Test category filtering
    const coreMetadata = metadataManager.getMetadataByCategory('core');
    if (Object.keys(coreMetadata).length === 0) {
      warnings.push('No core metadata found');
    }

  } catch (error) {
    errors.push(`Metadata system test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    success: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Test preloading system functionality
 */
async function testPreloadingSystem(): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];

  try {
    // Test preloader statistics
    const stats = routePreloader.getStatistics();
    if (typeof stats.preloadedCount !== 'number') {
      errors.push('Preloader statistics not working');
    }

    // Test high-priority route identification
    const highPriorityRoutes = routeRegistry.getHighPriorityRoutes();
    if (highPriorityRoutes.length === 0) {
      errors.push('No high-priority routes found');
    }

    // Test preloading a route
    const testRoute = routeRegistry.getAllRoutes()[0];
    if (testRoute) {
      const loader = routeRegistry.getRouteLoader(testRoute);
      if (loader) {
        routePreloader.preloadRoute(testRoute, loader, 1);
        // Give it a moment to process
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

  } catch (error) {
    errors.push(`Preloading system test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    success: errors.length === 0,
    errors
  };
}

/**
 * Generate integration test report
 */
export function generateIntegrationReport(result: IntegrationTestResult): string {
  const report = [
    '# Modular Route System Integration Test Report',
    `Generated: ${result.timestamp}`,
    `Status: ${result.success ? '✅ PASSED' : '❌ FAILED'}`,
    '',
    '## Summary',
    `- Total Routes: ${result.summary.totalRoutes}`,
    `- Valid Routes: ${result.summary.validRoutes}`,
    `- Tested Routes: ${result.summary.testedRoutes}`,
    `- Metadata Routes: ${result.summary.metadataRoutes}`,
    `- Preloaded Routes: ${result.summary.preloadedRoutes}`,
    '',
    '## Test Results',
    `- Module Validation: ${result.details.moduleValidation ? '✅' : '❌'}`,
    `- Route Validation: ${result.details.routeValidation ? '✅' : '❌'}`,
    `- Route Testing: ${result.details.routeTesting ? '✅' : '❌'}`,
    `- Metadata Validation: ${result.details.metadataValidation ? '✅' : '❌'}`,
    `- Preloading Test: ${result.details.preloadingTest ? '✅' : '❌'}`,
    '',
    '## Performance',
    `- Validation Time: ${result.performance.validationTime}ms`,
    `- Testing Time: ${result.performance.testingTime}ms`,
    `- Total Time: ${result.performance.totalTime}ms`,
    ''
  ];

  if (result.errors.length > 0) {
    report.push(
      '## Errors',
      ...result.errors.map(error => `- ${error}`),
      ''
    );
  }

  if (result.warnings.length > 0) {
    report.push(
      '## Warnings',
      ...result.warnings.map(warning => `- ${warning}`),
      ''
    );
  }

  return report.join('\n');
}

// Export for use in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).testRouteSystem = runIntegrationTest;
}
