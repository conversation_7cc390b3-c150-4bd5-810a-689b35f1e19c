/**
 * Route performance optimization system
 * Implements intelligent preloading, bundle splitting, and performance monitoring
 */

import { routeRegistry } from '../modules';
import { routePreloader } from '../enhanced-route-loader';
import { metadataManager } from '../metadata';

export interface PerformanceMetrics {
  routePath: string;
  loadTime: number;
  bundleSize?: number;
  cacheHit: boolean;
  timestamp: number;
  userAgent?: string;
}

export interface OptimizationStrategy {
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  execute: () => Promise<void>;
}

export interface PerformanceReport {
  timestamp: string;
  summary: {
    totalRoutes: number;
    preloadedRoutes: number;
    averageLoadTime: number;
    slowestRoutes: Array<{ path: string; loadTime: number }>;
    fastestRoutes: Array<{ path: string; loadTime: number }>;
  };
  optimizations: {
    applied: string[];
    available: string[];
    recommendations: string[];
  };
  metrics: PerformanceMetrics[];
}

/**
 * Route Performance Optimizer
 */
export class RoutePerformanceOptimizer {
  private static instance: RoutePerformanceOptimizer;
  private metrics: Map<string, PerformanceMetrics[]> = new Map();
  private strategies: Map<string, OptimizationStrategy> = new Map();
  private isOptimizing = false;

  static getInstance(): RoutePerformanceOptimizer {
    if (!RoutePerformanceOptimizer.instance) {
      RoutePerformanceOptimizer.instance = new RoutePerformanceOptimizer();
    }
    return RoutePerformanceOptimizer.instance;
  }

  constructor() {
    this.initializeStrategies();
    this.startPerformanceMonitoring();
  }

  /**
   * Initialize optimization strategies
   */
  private initializeStrategies(): void {
    // Strategy 1: Intelligent Preloading
    this.strategies.set('intelligent-preloading', {
      name: 'Intelligent Preloading',
      description: 'Preload routes based on user behavior and priority',
      enabled: true,
      priority: 10,
      execute: async () => {
        await this.executeIntelligentPreloading();
      }
    });

    // Strategy 2: Critical Path Optimization
    this.strategies.set('critical-path', {
      name: 'Critical Path Optimization',
      description: 'Optimize loading of critical user journey routes',
      enabled: true,
      priority: 9,
      execute: async () => {
        await this.executeCriticalPathOptimization();
      }
    });

    // Strategy 3: Bundle Size Optimization
    this.strategies.set('bundle-optimization', {
      name: 'Bundle Size Optimization',
      description: 'Optimize bundle sizes for faster loading',
      enabled: true,
      priority: 8,
      execute: async () => {
        await this.executeBundleOptimization();
      }
    });

    // Strategy 4: Cache Optimization
    this.strategies.set('cache-optimization', {
      name: 'Cache Optimization',
      description: 'Optimize caching strategies for better performance',
      enabled: true,
      priority: 7,
      execute: async () => {
        await this.executeCacheOptimization();
      }
    });

    // Strategy 5: Network-Aware Loading
    this.strategies.set('network-aware', {
      name: 'Network-Aware Loading',
      description: 'Adapt loading strategies based on network conditions',
      enabled: true,
      priority: 6,
      execute: async () => {
        await this.executeNetworkAwareLoading();
      }
    });
  }

  /**
   * Execute intelligent preloading strategy
   */
  private async executeIntelligentPreloading(): Promise<void> {
    // Get high-priority routes
    const highPriorityRoutes = routeRegistry.getHighPriorityRoutes();
    
    // Get user behavior data (mock implementation)
    const userBehaviorRoutes = this.getUserBehaviorRoutes();
    
    // Combine and prioritize routes
    const routesToPreload = [...new Set([...highPriorityRoutes, ...userBehaviorRoutes])];
    
    // Preload routes with intelligent scheduling
    for (const path of routesToPreload) {
      const loader = routeRegistry.getRouteLoader(path);
      if (loader) {
        const priority = highPriorityRoutes.includes(path) ? 10 : 5;
        routePreloader.preloadRoute(path, loader, priority);
      }
    }
  }

  /**
   * Execute critical path optimization
   */
  private async executeCriticalPathOptimization(): Promise<void> {
    // Define critical user journeys
    const criticalPaths = [
      '/appointments',
      '/contact',
      '/patient-resources',
      '/expertise'
    ];

    // Preload critical paths with highest priority
    for (const path of criticalPaths) {
      const loader = routeRegistry.getRouteLoader(path);
      if (loader) {
        routePreloader.preloadRoute(path, loader, 15);
      }
    }
  }

  /**
   * Execute bundle optimization
   */
  private async executeBundleOptimization(): Promise<void> {
    // Analyze bundle sizes (mock implementation)
    const bundleAnalysis = await this.analyzeBundleSizes();
    
    // Identify large bundles that could be split
    const largeBundles = bundleAnalysis.filter(bundle => bundle.size > 100000); // 100KB threshold
    
    if (largeBundles.length > 0) {
      console.log(`📦 Found ${largeBundles.length} large bundles that could benefit from splitting`);
    }
  }

  /**
   * Execute cache optimization
   */
  private async executeCacheOptimization(): Promise<void> {
    // Implement service worker caching strategies
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready;
        // Send cache optimization message to service worker
        registration.active?.postMessage({
          type: 'OPTIMIZE_CACHE',
          routes: routeRegistry.getAllRoutes()
        });
      } catch (error) {
        console.warn('Cache optimization failed:', error);
      }
    }
  }

  /**
   * Execute network-aware loading
   */
  private async executeNetworkAwareLoading(): Promise<void> {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      const effectiveType = connection?.effectiveType;
      
      // Adjust preloading strategy based on network speed
      if (effectiveType === 'slow-2g' || effectiveType === '2g') {
        // Disable non-critical preloading on slow networks
        console.log('🐌 Slow network detected, reducing preloading');
        this.strategies.get('intelligent-preloading')!.enabled = false;
      } else if (effectiveType === '4g') {
        // Aggressive preloading on fast networks
        console.log('🚀 Fast network detected, enabling aggressive preloading');
        await this.executeAggressivePreloading();
      }
    }
  }

  /**
   * Execute aggressive preloading for fast networks
   */
  private async executeAggressivePreloading(): Promise<void> {
    const allRoutes = routeRegistry.getAllRoutes();
    const metadata = metadataManager.getAllMetadata();
    
    // Preload all high and medium priority routes
    for (const path of allRoutes) {
      const routeMetadata = metadata[path];
      if (routeMetadata && (routeMetadata.priority === 'high' || routeMetadata.priority === 'medium')) {
        const loader = routeRegistry.getRouteLoader(path);
        if (loader) {
          const priority = routeMetadata.priority === 'high' ? 8 : 4;
          routePreloader.preloadRoute(path, loader, priority);
        }
      }
    }
  }

  /**
   * Record performance metrics
   */
  recordMetric(metric: PerformanceMetrics): void {
    if (!this.metrics.has(metric.routePath)) {
      this.metrics.set(metric.routePath, []);
    }
    
    const routeMetrics = this.metrics.get(metric.routePath)!;
    routeMetrics.push(metric);
    
    // Keep only last 100 metrics per route
    if (routeMetrics.length > 100) {
      routeMetrics.splice(0, routeMetrics.length - 100);
    }
  }

  /**
   * Get performance metrics for a route
   */
  getRouteMetrics(path: string): PerformanceMetrics[] {
    return this.metrics.get(path) || [];
  }

  /**
   * Get average load time for a route
   */
  getAverageLoadTime(path: string): number {
    const metrics = this.getRouteMetrics(path);
    if (metrics.length === 0) return 0;
    
    const totalTime = metrics.reduce((sum, metric) => sum + metric.loadTime, 0);
    return totalTime / metrics.length;
  }

  /**
   * Generate performance report
   */
  generatePerformanceReport(): PerformanceReport {
    const allRoutes = routeRegistry.getAllRoutes();
    const allMetrics: PerformanceMetrics[] = [];
    
    // Collect all metrics
    this.metrics.forEach(routeMetrics => {
      allMetrics.push(...routeMetrics);
    });

    // Calculate averages and extremes
    const loadTimes = allMetrics.map(m => m.loadTime);
    const averageLoadTime = loadTimes.length > 0 ? 
      loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length : 0;

    // Get slowest and fastest routes
    const routeAverages = allRoutes.map(path => ({
      path,
      loadTime: this.getAverageLoadTime(path)
    })).filter(route => route.loadTime > 0);

    const slowestRoutes = routeAverages
      .sort((a, b) => b.loadTime - a.loadTime)
      .slice(0, 5);

    const fastestRoutes = routeAverages
      .sort((a, b) => a.loadTime - b.loadTime)
      .slice(0, 5);

    // Get optimization status
    const appliedOptimizations = Array.from(this.strategies.values())
      .filter(strategy => strategy.enabled)
      .map(strategy => strategy.name);

    const availableOptimizations = Array.from(this.strategies.values())
      .filter(strategy => !strategy.enabled)
      .map(strategy => strategy.name);

    return {
      timestamp: new Date().toISOString(),
      summary: {
        totalRoutes: allRoutes.length,
        preloadedRoutes: routePreloader.getStatistics().preloadedCount,
        averageLoadTime,
        slowestRoutes,
        fastestRoutes
      },
      optimizations: {
        applied: appliedOptimizations,
        available: availableOptimizations,
        recommendations: this.generateRecommendations()
      },
      metrics: allMetrics
    };
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const report = this.generatePerformanceReport();

    if (report.summary.averageLoadTime > 1000) {
      recommendations.push('Consider implementing more aggressive preloading for better performance');
    }

    if (report.summary.preloadedRoutes < report.summary.totalRoutes * 0.3) {
      recommendations.push('Increase preloading coverage for critical routes');
    }

    if (report.summary.slowestRoutes.length > 0) {
      const slowest = report.summary.slowestRoutes[0];
      if (slowest.loadTime > 2000) {
        recommendations.push(`Optimize ${slowest.path} - it's taking ${slowest.loadTime.toFixed(0)}ms to load`);
      }
    }

    return recommendations;
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Monitor navigation performance
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          // Record navigation metrics
          this.recordMetric({
            routePath: window.location.pathname,
            loadTime: entry.duration,
            cacheHit: false,
            timestamp: Date.now(),
            userAgent: navigator.userAgent
          });
        }
      }
    });

    observer.observe({ entryTypes: ['navigation'] });
  }

  /**
   * Mock implementation for user behavior analysis
   */
  private getUserBehaviorRoutes(): string[] {
    // In a real implementation, this would analyze user navigation patterns
    return [
      '/patient-resources/spine-conditions',
      '/expertise/cervical-disc-replacement',
      '/locations/surrey-hills'
    ];
  }

  /**
   * Mock implementation for bundle size analysis
   */
  private async analyzeBundleSizes(): Promise<Array<{ path: string; size: number }>> {
    // In a real implementation, this would analyze actual bundle sizes
    return [
      { path: '/patient-resources', size: 150000 },
      { path: '/expertise', size: 120000 },
      { path: '/locations', size: 80000 }
    ];
  }

  /**
   * Apply all enabled optimization strategies
   */
  async applyOptimizations(): Promise<void> {
    if (this.isOptimizing) return;
    
    this.isOptimizing = true;
    console.log('🚀 Applying route performance optimizations...');

    try {
      const enabledStrategies = Array.from(this.strategies.values())
        .filter(strategy => strategy.enabled)
        .sort((a, b) => b.priority - a.priority);

      for (const strategy of enabledStrategies) {
        try {
          await strategy.execute();
          console.log(`✅ Applied: ${strategy.name}`);
        } catch (error) {
          console.warn(`❌ Failed to apply ${strategy.name}:`, error);
        }
      }
    } finally {
      this.isOptimizing = false;
    }
  }
}

// Export singleton instance
export const routeOptimizer = RoutePerformanceOptimizer.getInstance();

// Auto-apply optimizations when the module loads
if (typeof window !== 'undefined') {
  // Delay to avoid blocking initial load
  setTimeout(() => {
    routeOptimizer.applyOptimizations();
  }, 2000);
}
