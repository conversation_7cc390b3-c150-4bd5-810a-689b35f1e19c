/**
 * CSS-First Responsive Utilities
 * Performance-optimized responsive design patterns
 */

/* CSS Custom Properties for Responsive Design */
:root {
  /* Breakpoint values */
  --breakpoint-mobile: 768px;
  --breakpoint-tablet: 1024px;
  --breakpoint-desktop: 1280px;
  --breakpoint-large-desktop: 1536px;
  
  /* Container sizes */
  --container-mobile: 100%;
  --container-tablet: 90%;
  --container-desktop: 1200px;
  --container-large: 1400px;
  
  /* Mobile spacing scale */
  --spacing-mobile-xs: 0.25rem;
  --spacing-mobile-sm: 0.5rem;
  --spacing-mobile-md: 1rem;
  --spacing-mobile-lg: 1.5rem;
  --spacing-mobile-xl: 2rem;
  --spacing-mobile-2xl: 3rem;
  --spacing-mobile-3xl: 4rem;
  
  /* Desktop spacing scale */
  --spacing-desktop-xs: 0.5rem;
  --spacing-desktop-sm: 1rem;
  --spacing-desktop-md: 1.5rem;
  --spacing-desktop-lg: 2rem;
  --spacing-desktop-xl: 3rem;
  --spacing-desktop-2xl: 4rem;
  --spacing-desktop-3xl: 6rem;
  
  /* Mobile typography scale */
  --text-mobile-xs: 0.75rem;
  --text-mobile-sm: 0.875rem;
  --text-mobile-base: 1rem;
  --text-mobile-lg: 1.125rem;
  --text-mobile-xl: 1.25rem;
  --text-mobile-2xl: 1.5rem;
  --text-mobile-3xl: 1.875rem;
  
  /* Desktop typography scale */
  --text-desktop-xs: 0.75rem;
  --text-desktop-sm: 0.875rem;
  --text-desktop-base: 1rem;
  --text-desktop-lg: 1.125rem;
  --text-desktop-xl: 1.25rem;
  --text-desktop-2xl: 1.5rem;
  --text-desktop-3xl: 1.875rem;
  --text-desktop-4xl: 2.25rem;
  --text-desktop-5xl: 3rem;
}

/* Responsive Container Patterns */
.responsive-container {
  width: 100%;
  max-width: var(--container-mobile);
  margin: 0 auto;
  padding: 0 var(--spacing-mobile-md);
  contain: layout style; /* Performance optimization */
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: var(--container-tablet);
    padding: 0 var(--spacing-desktop-md);
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: var(--container-desktop);
    padding: 0 var(--spacing-desktop-lg);
  }
}

/* Mobile-specific container */
.mobile-container {
  width: 100%;
  padding: 0 var(--spacing-mobile-md);
  contain: layout style;
}

/* Responsive Grid Patterns */
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-mobile-md);
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-desktop-md);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-desktop-lg);
  }
}

/* Responsive Typography */
.responsive-text {
  font-size: var(--text-mobile-base);
  line-height: 1.5;
}

@media (min-width: 768px) {
  .responsive-text {
    font-size: var(--text-desktop-base);
    line-height: 1.6;
  }
}

.responsive-heading {
  font-size: var(--text-mobile-2xl);
  line-height: 1.2;
}

@media (min-width: 768px) {
  .responsive-heading {
    font-size: var(--text-desktop-3xl);
    line-height: 1.3;
  }
}

@media (min-width: 1280px) {
  .responsive-heading {
    font-size: var(--text-desktop-4xl);
  }
}

/* Responsive Spacing */
.responsive-section {
  padding: var(--spacing-mobile-xl) 0;
}

@media (min-width: 768px) {
  .responsive-section {
    padding: var(--spacing-desktop-xl) 0;
  }
}

@media (min-width: 1280px) {
  .responsive-section {
    padding: var(--spacing-desktop-2xl) 0;
  }
}

/* Mobile-specific spacing classes */
.mobile-section-sm {
  padding: var(--spacing-mobile-sm) 0;
}

.mobile-section {
  padding: var(--spacing-mobile-md) 0;
}

.mobile-section-lg {
  padding: var(--spacing-mobile-lg) 0;
}

.mobile-section-xl {
  padding: var(--spacing-mobile-xl) 0;
}

/* Mobile gap classes */
.gap-mobile-sm {
  gap: var(--spacing-mobile-sm);
}

.gap-mobile-md {
  gap: var(--spacing-mobile-md);
}

.gap-mobile-lg {
  gap: var(--spacing-mobile-lg);
}

.gap-mobile-xl {
  gap: var(--spacing-mobile-xl);
}

/* Mobile space-y classes */
.space-y-mobile-sm > * + * {
  margin-top: var(--spacing-mobile-sm);
}

.space-y-mobile-md > * + * {
  margin-top: var(--spacing-mobile-md);
}

.space-y-mobile-lg > * + * {
  margin-top: var(--spacing-mobile-lg);
}

.space-y-mobile-xl > * + * {
  margin-top: var(--spacing-mobile-xl);
}

/* Mobile padding classes */
.p-mobile-sm {
  padding: var(--spacing-mobile-sm);
}

.p-mobile-md {
  padding: var(--spacing-mobile-md);
}

.p-mobile-lg {
  padding: var(--spacing-mobile-lg);
}

/* Touch-friendly patterns */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px;
}

@media (hover: hover) {
  .touch-target:hover {
    background-color: var(--color-hover, rgba(0, 0, 0, 0.05));
  }
}

@media (pointer: coarse) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
    padding: 16px;
  }
}

/* Touch feedback for mobile */
.touch-feedback {
  transition: transform 0.1s ease;
}

.touch-feedback:active {
  transform: scale(0.98);
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .motion-reduce\:animate-none {
    animation: none !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-optimized {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode responsive adjustments */
@media (prefers-color-scheme: dark) {
  .responsive-container {
    --color-hover: rgba(255, 255, 255, 0.1);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .responsive-container {
    border: 1px solid;
  }
}

/* Print styles */
@media print {
  .responsive-container {
    max-width: none;
    padding: 0;
  }
  
  .responsive-section {
    padding: 1rem 0;
  }
  
  .touch-target {
    min-height: auto;
    min-width: auto;
    padding: 0.25rem;
  }
}

/* Utility classes for common responsive patterns */
.hide-mobile {
  display: block;
}

@media (max-width: 767px) {
  .hide-mobile {
    display: none;
  }
}

.show-mobile {
  display: none;
}

@media (max-width: 767px) {
  .show-mobile {
    display: block;
  }
}

.hide-desktop {
  display: none;
}

@media (min-width: 768px) {
  .hide-desktop {
    display: block;
  }
}

.show-desktop {
  display: block;
}

@media (min-width: 768px) {
  .show-desktop {
    display: none;
  }
}
