# Device Context Best Practices Guide

## Overview

This guide provides comprehensive guidelines for optimal device context usage, performance optimization strategies, and best practices to prevent over-usage and excessive re-renders.

## Core Principles

### 1. CSS-First Approach
**Always prefer CSS media queries over JavaScript device detection when possible.**

```css
/* ✅ Good - CSS media query */
.responsive-container {
  padding: 1rem;
}

@media (min-width: 768px) {
  .responsive-container {
    padding: 2rem;
  }
}
```

```tsx
// ❌ Bad - Unnecessary JavaScript
const { isMobile } = useDeviceDetection();
return (
  <div className={isMobile ? 'p-4' : 'p-8'}>
    {children}
  </div>
);
```

### 2. Selective Hook Usage
**Use specific hooks instead of the full device context.**

```tsx
// ✅ Good - Selective hooks
const isMobile = useIsMobile();
const orientation = useOrientation();

// ❌ Bad - Full context access
const { isMobile, orientation, width, height, isTablet } = useDeviceDetection();
```

### 3. Memoization and Performance
**Always memoize components that use device context.**

```tsx
// ✅ Good - Memoized component
const ResponsiveComponent = React.memo(({ children }) => {
  const isMobile = useIsMobile();
  return <div className={isMobile ? 'mobile-layout' : 'desktop-layout'}>{children}</div>;
});

// ❌ Bad - No memoization
const ResponsiveComponent = ({ children }) => {
  const isMobile = useIsMobile();
  return <div className={isMobile ? 'mobile-layout' : 'desktop-layout'}>{children}</div>;
};
```

## When to Use CSS vs JavaScript

### Use CSS For:
- **Layout changes** (grid, flexbox, positioning)
- **Typography scaling** (font sizes, line heights)
- **Spacing adjustments** (margins, padding)
- **Color scheme changes** (themes, dark mode)
- **Simple show/hide patterns**
- **Basic animations and transitions**
- **Print styles**
- **Accessibility preferences** (reduced motion, high contrast)

### Use JavaScript For:
- **Complex conditional logic** based on device properties
- **Dynamic content loading** based on device capabilities
- **Touch gesture handling** and interaction patterns
- **Viewport calculations** for complex layouts
- **Device-specific API access** (camera, geolocation)
- **Performance optimizations** based on device capabilities
- **Complex state management** tied to device changes
- **Real-time responsive adjustments**

## Selective Hook Patterns

### Available Selective Hooks

```tsx
// Device type detection
const isMobile = useIsMobile();
const isTablet = useIsTablet();
const isDesktop = useIsDesktop();

// Capabilities
const isTouchDevice = useIsTouchDevice();
const hasHover = useHasHover();

// Orientation and size
const orientation = useOrientation();
const screenSize = useScreenSize();
const { width, height } = useViewportSize();
const width = useViewportWidth();
const height = useViewportHeight();

// Breakpoint-based
const breakpoint = useBreakpoint(); // 'mobile' | 'tablet' | 'desktop'

// Responsive values
const padding = useResponsiveValue({
  mobile: '1rem',
  tablet: '1.5rem',
  desktop: '2rem'
});
```

### Hook Selection Guidelines

```tsx
// ✅ Good - Use specific hook for specific need
const NavigationMenu = () => {
  const isMobile = useIsMobile(); // Only subscribes to mobile state
  return isMobile ? <MobileMenu /> : <DesktopMenu />;
};

// ✅ Good - Use responsive value hook for value selection
const Card = ({ children }) => {
  const padding = useResponsiveValue({
    mobile: 'p-4',
    desktop: 'p-6'
  });
  return <div className={padding}>{children}</div>;
};

// ❌ Bad - Using full context for single property
const NavigationMenu = () => {
  const { isMobile } = useDeviceDetection(); // Subscribes to all device changes
  return isMobile ? <MobileMenu /> : <DesktopMenu />;
};
```

## Performance Optimization Strategies

### 1. Component Memoization

```tsx
// ✅ Memoize components using device context
const ResponsiveGrid = React.memo(({ children, columns }) => {
  const isMobile = useIsMobile();
  const gridColumns = isMobile ? 1 : columns;
  
  return (
    <div 
      className="grid gap-4"
      style={{ gridTemplateColumns: `repeat(${gridColumns}, 1fr)` }}
    >
      {children}
    </div>
  );
});

// ✅ Memoize expensive calculations
const ComplexLayout = React.memo(({ items }) => {
  const { width, height } = useViewportSize();
  
  const layout = useMemo(() => {
    return calculateComplexLayout(items, width, height);
  }, [items, width, height]);
  
  return <div>{/* render layout */}</div>;
});
```

### 2. Debouncing and Throttling

```tsx
// ✅ Use debounced values for expensive operations
const SearchResults = () => {
  const { width } = useViewportSize();
  const debouncedWidth = useDebounce(width, 300);
  
  const columns = useMemo(() => {
    return Math.floor(debouncedWidth / 300);
  }, [debouncedWidth]);
  
  return <GridLayout columns={columns} />;
};
```

### 3. Conditional Rendering Optimization

```tsx
// ✅ Good - Minimize re-renders with early returns
const MobileOnlyComponent = React.memo(() => {
  const isMobile = useIsMobile();
  
  if (!isMobile) {
    return null; // Early return prevents unnecessary renders
  }
  
  return <ExpensiveMobileComponent />;
});

// ❌ Bad - Always renders, then conditionally displays
const MobileOnlyComponent = () => {
  const isMobile = useIsMobile();
  
  return (
    <div style={{ display: isMobile ? 'block' : 'none' }}>
      <ExpensiveMobileComponent />
    </div>
  );
};
```

## Common Anti-Patterns to Avoid

### 1. Over-Using Device Context

```tsx
// ❌ Bad - Using device context for simple styling
const Button = ({ children }) => {
  const { isMobile } = useDeviceDetection();
  return (
    <button className={isMobile ? 'btn-mobile' : 'btn-desktop'}>
      {children}
    </button>
  );
};

// ✅ Good - Use CSS classes
const Button = ({ children }) => {
  return (
    <button className="btn-responsive">
      {children}
    </button>
  );
};
```

### 2. Unnecessary Re-renders

```tsx
// ❌ Bad - Creates new object on every render
const ResponsiveComponent = () => {
  const { isMobile } = useDeviceDetection();
  const styles = { padding: isMobile ? '1rem' : '2rem' }; // New object every render
  return <div style={styles}>Content</div>;
};

// ✅ Good - Memoized styles
const ResponsiveComponent = () => {
  const isMobile = useIsMobile();
  const styles = useMemo(() => ({
    padding: isMobile ? '1rem' : '2rem'
  }), [isMobile]);
  return <div style={styles}>Content</div>;
};
```

### 3. Inefficient Hook Usage

```tsx
// ❌ Bad - Multiple device context calls
const Component = () => {
  const { isMobile } = useDeviceDetection();
  const { isTablet } = useDeviceDetection();
  const { width } = useDeviceDetection();
  // Multiple subscriptions to the same context
};

// ✅ Good - Single context call or selective hooks
const Component = () => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const width = useViewportWidth();
  // Each hook subscribes only to specific properties
};
```

## Performance Monitoring

### Development Monitoring

```tsx
// Enable performance monitoring in development
import { DeviceContextPerformanceDashboard } from '@/lib/device-context-monitor';

function App() {
  return (
    <div>
      {/* Your app content */}
      {process.env.NODE_ENV === 'development' && (
        <DeviceContextPerformanceDashboard />
      )}
    </div>
  );
}
```

### Component-Level Monitoring

```tsx
// Monitor specific components
import { useDeviceContextMonitor } from '@/lib/device-context-monitor';

const MonitoredComponent = () => {
  const isMobile = useIsMobile();
  
  // Track this component's device context usage
  useDeviceContextMonitor('MonitoredComponent', ['isMobile']);
  
  return <div>{/* component content */}</div>;
};
```

## Testing Device Context Usage

### Unit Testing

```tsx
// Test responsive behavior
import { render } from '@testing-library/react';
import { DeviceProvider } from '@/contexts/DeviceContext';

const renderWithDevice = (component, deviceProps = {}) => {
  const defaultProps = {
    debounceDelay: 0, // Disable debouncing in tests
    enablePerformanceMonitoring: false
  };
  
  return render(
    <DeviceProvider {...defaultProps} {...deviceProps}>
      {component}
    </DeviceProvider>
  );
};

test('renders mobile layout on mobile devices', () => {
  // Mock window.innerWidth
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: 375,
  });
  
  const { getByTestId } = renderWithDevice(<ResponsiveComponent />);
  expect(getByTestId('mobile-layout')).toBeInTheDocument();
});
```

### Performance Testing

```tsx
// Test for excessive re-renders
import { renderHook } from '@testing-library/react-hooks';

test('selective hooks minimize re-renders', () => {
  let renderCount = 0;
  
  const { result } = renderHook(() => {
    renderCount++;
    return useIsMobile();
  });
  
  // Simulate resize that doesn't affect mobile state
  act(() => {
    window.innerWidth = 1200;
    window.dispatchEvent(new Event('resize'));
  });
  
  // Should not cause additional renders if mobile state unchanged
  expect(renderCount).toBe(1);
});
```

## Migration Guide

### From Full Context to Selective Hooks

```tsx
// Before
const Component = () => {
  const { isMobile, isTablet, width } = useDeviceDetection();
  // Component logic
};

// After
const Component = () => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const width = useViewportWidth();
  // Component logic
};
```

### From JavaScript to CSS

```tsx
// Before
const Component = () => {
  const { isMobile } = useDeviceDetection();
  return (
    <div className={`container ${isMobile ? 'mobile-padding' : 'desktop-padding'}`}>
      {children}
    </div>
  );
};

// After
const Component = () => {
  return (
    <div className="responsive-container">
      {children}
    </div>
  );
};
```

## Checklist for Device Context Usage

### Before Using Device Context:
- [ ] Can this be achieved with CSS media queries?
- [ ] Do I need the full device context or just specific properties?
- [ ] Is this component memoized?
- [ ] Am I avoiding creating new objects on every render?
- [ ] Is this the most performant approach?

### Code Review Checklist:
- [ ] Components using device context are memoized
- [ ] Selective hooks are used instead of full context
- [ ] CSS is preferred for simple responsive changes
- [ ] No unnecessary re-renders are triggered
- [ ] Performance monitoring is enabled in development
- [ ] Tests cover responsive behavior

## Summary

Following these best practices will ensure:
- **Optimal performance** with minimal re-renders
- **Better maintainability** with clear separation of concerns
- **Improved user experience** with faster, more responsive interfaces
- **Easier debugging** with performance monitoring tools
- **Future-proof code** that scales with application growth

Remember: **CSS first, JavaScript when necessary, selective hooks always.**
