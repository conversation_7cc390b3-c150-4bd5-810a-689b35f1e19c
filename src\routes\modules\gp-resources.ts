/**
 * GP Resources routes module
 * Contains all general practitioner resource pages
 */

import { RouteModule } from './types';
import { ROUTE_PATHS } from '../route-definitions';

export const gpResourcesModule: RouteModule = {
  name: 'gp-resources',
  category: 'gp-resources',
  priority: 'medium',
  preload: [
    ROUTE_PATHS.GP_RESOURCES,
    ROUTE_PATHS.GP_RESOURCES_ROUTES.REFERRAL_PROTOCOLS
  ],
  routes: {
    // Main GP resources page
    [ROUTE_PATHS.GP_RESOURCES]: () => import('@/pages/GPResources'),

    // GP resource pages
    [ROUTE_PATHS.GP_RESOURCES_ROUTES.REFERRAL_PROTOCOLS]: () => import('@/pages/gp-resources/ReferralProtocols'),
    [ROUTE_PATHS.GP_RESOURCES_ROUTES.DIAGNOSTICS]: () => import('@/pages/gp-resources/Diagnostics'),
    [ROUTE_PATHS.GP_RESOURCES_ROUTES.CARE_COORDINATION]: () => import('@/pages/gp-resources/CareCoordination'),
    [ROUTE_PATHS.GP_RESOURCES_ROUTES.EMERGENCIES]: () => import('@/pages/gp-resources/Emergencies'),
  }
};

// Export route paths for this module
export const gpResourcesRoutePaths = Object.keys(gpResourcesModule.routes);

// Export metadata for GP resources routes
export const gpResourcesRouteMetadata = {
  [ROUTE_PATHS.GP_RESOURCES]: {
    title: 'GP Resources',
    description: 'Resources and protocols for general practitioners',
    keywords: ['GP resources', 'referrals', 'protocols', 'healthcare'],
    category: 'gp-resources' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'gp-resources'
  },
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.REFERRAL_PROTOCOLS]: {
    title: 'Referral Protocols',
    description: 'Guidelines and protocols for referring patients to neurosurgical services',
    keywords: ['referral protocols', 'GP guidelines', 'neurosurgery referrals'],
    category: 'gp-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'gp-resources'
  },
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.DIAGNOSTICS]: {
    title: 'Diagnostic Guidelines',
    description: 'Diagnostic guidelines and imaging protocols for neurosurgical conditions',
    keywords: ['diagnostics', 'imaging', 'MRI', 'CT scan', 'neurosurgery'],
    category: 'gp-resources' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'gp-resources'
  },
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.CARE_COORDINATION]: {
    title: 'Care Coordination',
    description: 'Guidelines for coordinating patient care with neurosurgical services',
    keywords: ['care coordination', 'patient management', 'healthcare collaboration'],
    category: 'gp-resources' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'gp-resources'
  },
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.EMERGENCIES]: {
    title: 'Emergency Protocols',
    description: 'Emergency protocols and urgent referral guidelines for neurosurgical emergencies',
    keywords: ['emergency protocols', 'urgent referrals', 'neurosurgical emergencies'],
    category: 'gp-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'gp-resources'
  }
};
