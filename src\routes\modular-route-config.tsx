/**
 * Modular Route Configuration
 * New implementation using the modular route system
 */

import React from 'react';
import { Navigate } from 'react-router-dom';

// Import the new modular route system
import { 
  routeRegistry, 
  getAllRoutes as getModularRoutes,
  getRouteLoader,
  getHighPriorityRoutes,
  getRouteRedirects,
  validateRoutes,
  getAllRouteMetadata
} from './modules';

// Import enhanced route loader
import { createEnhancedLazyComponent, routePreloader } from './enhanced-route-loader';

// Import legacy system for backward compatibility
import { ROUTE_PATHS } from './route-definitions';
import { logRoute } from '@/lib/dev-console';

// Initialize the modular route system
const modularRegistry = routeRegistry;

// Validate routes on initialization
const validation = validateRoutes();
if (!validation.isValid) {
  console.error('Route validation failed:', validation.errors);
  if (validation.warnings.length > 0) {
    console.warn('Route validation warnings:', validation.warnings);
  }
} else {
  logRoute('✅ All routes validated successfully');
}

// Route configuration interface (compatible with React Router)
export interface RouteConfig {
  path: string;
  element: React.ReactNode | null;
  children?: RouteConfig[];
}

/**
 * Create route element using the enhanced lazy loader
 */
function createModularRouteElement(path: string): React.ReactNode | null {
  try {
    const loader = getRouteLoader(path);
    if (!loader) {
      logRoute(`No loader found for route: ${path}`);
      return null;
    }

    const LazyComponent = createEnhancedLazyComponent(loader, {
      routePath: path,
      retryAttempts: 3,
      retryDelay: 1000
    });

    return <LazyComponent />;
  } catch (error) {
    logRoute(`Failed to create route element for path: ${path}`, error);
    return null;
  }
}

/**
 * Create redirect element
 */
function createRedirectElement(to: string): React.ReactNode | null {
  try {
    return <Navigate to={to} replace />;
  } catch (error) {
    logRoute(`Failed to create redirect element to: ${to}`, error);
    return null;
  }
}

/**
 * Generate routes from the modular system
 */
function generateModularRoutes(): RouteConfig[] {
  const routes: RouteConfig[] = [];
  const allRoutes = getModularRoutes();
  const redirects = getRouteRedirects();

  // Add all registered routes
  allRoutes.forEach(path => {
    routes.push({
      path,
      element: createModularRouteElement(path)
    });
  });

  // Add redirects
  Object.entries(redirects).forEach(([from, to]) => {
    routes.push({
      path: from,
      element: createRedirectElement(to)
    });
  });

  return routes;
}

// Generate the routes
export const modularRoutes: RouteConfig[] = generateModularRoutes();

/**
 * Get all routes (main export function)
 */
export const getAllRoutes = (): RouteConfig[] => {
  return modularRoutes;
};

/**
 * Initialize route preloading for high-priority routes
 */
export function initializeRoutePreloading(): void {
  const highPriorityRoutes = getHighPriorityRoutes();
  
  // Preload high-priority routes
  highPriorityRoutes.forEach(path => {
    const loader = getRouteLoader(path);
    if (loader) {
      routePreloader.preloadRoute(path, loader, 10); // High priority
    }
  });

  logRoute(`Initialized preloading for ${highPriorityRoutes.length} high-priority routes`);
}

/**
 * Get route statistics
 */
export function getRouteStatistics() {
  const stats = modularRegistry.getStatistics();
  const preloaderStats = routePreloader.getStatistics();
  
  return {
    ...stats,
    preloader: preloaderStats,
    validation
  };
}

/**
 * Get route metadata
 */
export function getRouteMetadata() {
  return getAllRouteMetadata();
}

// Initialize preloading when the module loads
if (typeof window !== 'undefined') {
  // Delay initialization to avoid blocking the main thread
  setTimeout(initializeRoutePreloading, 100);
}

// Export route utilities for backward compatibility
export { ROUTE_PATHS } from './route-definitions';
export { getRouteRedirects as ROUTE_REDIRECTS } from './modules';
export { routePreloader, createEnhancedLazyComponent } from './enhanced-route-loader';

// Export modular system components
export { 
  routeRegistry,
  getModularRoutes,
  getRouteLoader,
  getHighPriorityRoutes,
  validateRoutes
} from './modules';

// Log successful initialization
logRoute('✅ Modular route system initialized successfully');
logRoute(`📊 Route statistics:`, getRouteStatistics());
