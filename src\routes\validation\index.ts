/**
 * Route validation system exports
 */

// Validator exports
export {
  RouteValidator,
  routeValidator,
  validateAllRoutes,
  quickValidateRoutes,
  validateRoute,
  getValidationSummary
} from './route-validator';

export type { ValidationReport } from './route-validator';

// Tester exports
export {
  RouteTester,
  routeTester,
  testAllRoutes,
  testRoute,
  testModule,
  benchmarkRoutes
} from './route-tester';

export type { RouteTestResult, RouteTestSuite } from './route-tester';

// Combined validation and testing utilities
export async function runCompleteRouteValidation() {
  console.log('🔍 Starting comprehensive route validation...');
  
  // Quick validation first
  const quickResult = quickValidateRoutes();
  console.log('📋 Quick validation:', quickResult.isValid ? '✅ Passed' : '❌ Failed');
  
  if (!quickResult.isValid) {
    console.log('❌ Errors:', quickResult.errors);
    console.log('⚠️ Warnings:', quickResult.warnings);
  }
  
  // Full validation
  const fullReport = await validateAllRoutes();
  console.log('📊 Full validation completed');
  
  // Route testing
  const testResults = await testAllRoutes();
  console.log('🧪 Route testing completed');
  
  return {
    quickValidation: quickResult,
    fullValidation: fullReport,
    testing: testResults,
    summary: {
      allValid: quickResult.isValid && fullReport.summary.invalidRoutes === 0,
      allTested: testResults.failed === 0,
      overallHealth: quickResult.isValid && fullReport.summary.invalidRoutes === 0 && testResults.failed === 0
    }
  };
}
